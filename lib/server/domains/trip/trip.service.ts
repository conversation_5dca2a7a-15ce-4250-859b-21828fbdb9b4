import { <PERSON><PERSON><PERSON><PERSON>, Timestamp } from "firebase-admin/firestore"
import { Trip, TripCreateData, TripUpdateData, TripStatus } from "@/lib/domains/trip/trip.types"
import { UserTrip } from "@/lib/domains/user-trip/user-trip.types"
import { User } from "@/lib/domains/user/user.types"
import { normalizeToUTCMidnight } from "@/lib/utils/date-utils"
import { UserServerService } from "../user/user.service"
import { getAdminDb } from "@/lib/firebase-admin"

/**
 * Server-side Trip Service using Firebase Admin SDK
 */
export class TripServerService {
  private static readonly COLLECTION = "trips"

  /**
   * Serialize Firestore Timestamps to plain objects for SSR
   */
  private static serializeTimestamps(data: any): any {
    if (!data) return data

    if (data instanceof Timestamp) {
      return data.toDate()
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.serializeTimestamps(item))
    }

    if (typeof data === "object" && data !== null) {
      const serialized: any = {}
      for (const [key, value] of Object.entries(data)) {
        serialized[key] = this.serializeTimestamps(value)
      }
      return serialized
    }

    return data
  }
  private static readonly USER_TRIPS_COLLECTION = "userTrips"

  /**
   * Get a trip by ID
   * @param tripId Trip ID
   * @returns The trip data or null if not found
   */
  static async getTrip(tripId: string): Promise<Trip | null> {
    try {
      const adminDb = await getAdminDb()
      const tripDoc = await adminDb.collection(this.COLLECTION).doc(tripId).get()

      if (!tripDoc.exists) {
        return null
      }

      const data = tripDoc.data()
      if (!data) {
        return null
      }

      // Serialize timestamps for SSR
      const serializedData = this.serializeTimestamps(data)

      // Get the actual attendees from userTrips collection
      const attendees = await this.getTripAttendees(tripId)

      return {
        ...serializedData,
        id: tripId,
        attendees,
        tasksCompleted: typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0,
        totalTasks: typeof data.totalTasks === "number" ? data.totalTasks : 0,
      } as Trip
    } catch (error) {
      console.error("Error getting trip (server):", error)
      throw error
    }
  }

  /**
   * Get multiple trips by IDs
   * @param tripIds Array of trip IDs
   * @returns Array of trips
   */
  static async getTripsFromIds(tripIds: string[]): Promise<Trip[]> {
    try {
      const adminDb = await getAdminDb()
      if (tripIds.length === 0) {
        return []
      }

      const trips: Trip[] = []

      // Firestore 'in' queries are limited to 10 items, so we need to batch
      const batchSize = 10
      for (let i = 0; i < tripIds.length; i += batchSize) {
        const batch = tripIds.slice(i, i + batchSize)
        const querySnapshot = await adminDb
          .collection(this.COLLECTION)
          .where(
            "__name__",
            "in",
            batch.map((id) => adminDb.collection(this.COLLECTION).doc(id))
          )
          .get()

        for (const doc of querySnapshot.docs) {
          const data = doc.data()
          const attendees = await this.getTripAttendees(doc.id)

          trips.push({
            ...data,
            id: doc.id,
            attendees,
            tasksCompleted: typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0,
            totalTasks: typeof data.totalTasks === "number" ? data.totalTasks : 0,
          } as Trip)
        }
      }

      return trips
    } catch (error) {
      console.error("Error getting trips from IDs (server):", error)
      throw error
    }
  }

  /**
   * Get trips for a squad
   * @param squadId Squad ID
   * @returns Array of trips
   */
  static async getSquadTrips(squadId: string): Promise<Trip[]> {
    try {
      const adminDb = await getAdminDb()
      const querySnapshot = await adminDb
        .collection(this.COLLECTION)
        .where("squadId", "==", squadId)
        .get()

      const trips = await Promise.all(
        querySnapshot.docs.map(async (doc: any) => {
          const data = doc.data()
          const serializedData = this.serializeTimestamps(data)
          const attendees = await this.getTripAttendees(doc.id)

          return {
            ...serializedData,
            id: doc.id,
            attendees,
            tasksCompleted: typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0,
            totalTasks: typeof data.totalTasks === "number" ? data.totalTasks : 0,
          } as Trip
        })
      )

      return trips
    } catch (error) {
      console.error("Error getting squad trips (server):", error)
      throw error
    }
  }

  /**
   * Get trips created by a user
   * @param userId User ID
   * @returns Array of trips
   */
  static async getUserCreatedTrips(userId: string): Promise<Trip[]> {
    try {
      const adminDb = await getAdminDb()
      const querySnapshot = await adminDb
        .collection(this.COLLECTION)
        .where("createdBy", "==", userId)
        .get()

      const trips = await Promise.all(
        querySnapshot.docs.map(async (doc) => {
          const data = doc.data()
          const attendees = await this.getTripAttendees(doc.id)

          return {
            ...data,
            id: doc.id,
            attendees,
            tasksCompleted: typeof data.tasksCompleted === "number" ? data.tasksCompleted : 0,
            totalTasks: typeof data.totalTasks === "number" ? data.totalTasks : 0,
          } as Trip
        })
      )

      return trips
    } catch (error) {
      console.error("Error getting user created trips (server):", error)
      throw error
    }
  }

  /**
   * Get trips where user is attending (status = "going")
   * @param userId User ID
   * @returns Array of trips
   */
  static async getUserAttendingTrips(userId: string): Promise<Trip[]> {
    try {
      const adminDb = await getAdminDb()

      // First get all userTrips where user is going
      const userTripsSnapshot = await adminDb
        .collection(this.USER_TRIPS_COLLECTION)
        .where("userId", "==", userId)
        .where("status", "==", "going")
        .get()

      if (userTripsSnapshot.empty) {
        return []
      }

      // Extract trip IDs
      const tripIds = userTripsSnapshot.docs.map((doc) => doc.data().tripId)

      // Get the actual trip documents
      return await this.getTripsFromIds(tripIds)
    } catch (error) {
      console.error("Error getting user attending trips (server):", error)
      throw error
    }
  }

  /**
   * Get upcoming trips for a user (attending trips that haven't ended)
   * @param userId User ID
   * @returns Array of upcoming trips
   */
  static async getUserUpcomingTrips(userId: string): Promise<Trip[]> {
    try {
      const attendingTrips = await this.getUserAttendingTrips(userId)
      const now = new Date()

      return attendingTrips.filter((trip) => {
        if (!trip.endDate) return false
        const endDate = trip.endDate instanceof Date ? trip.endDate : trip.endDate.toDate()
        return trip.status !== "completed" && endDate >= now
      })
    } catch (error) {
      console.error("Error getting user upcoming trips (server):", error)
      throw error
    }
  }

  /**
   * Get past trips for a user (attending trips that have ended or are completed)
   * @param userId User ID
   * @returns Array of past trips
   */
  static async getUserPastTrips(userId: string): Promise<Trip[]> {
    try {
      const attendingTrips = await this.getUserAttendingTrips(userId)
      const now = new Date()

      return attendingTrips.filter((trip) => {
        if (!trip.endDate) return false
        const endDate = trip.endDate instanceof Date ? trip.endDate : trip.endDate.toDate()
        return trip.status === "completed" || endDate < now
      })
    } catch (error) {
      console.error("Error getting user past trips (server):", error)
      throw error
    }
  }

  /**
   * Create a new trip
   * @param tripData Trip creation data
   * @returns The created trip ID
   */
  static async createTrip(tripData: TripCreateData): Promise<string> {
    try {
      const adminDb = await getAdminDb()
      const tripRef = adminDb.collection(this.COLLECTION).doc()
      const tripId = tripRef.id

      // Convert dates to Date objects if they're Timestamps, otherwise use as-is
      const startDate =
        tripData.startDate instanceof Date
          ? tripData.startDate
          : (tripData.startDate as any).toDate()
      const endDate =
        tripData.endDate instanceof Date ? tripData.endDate : (tripData.endDate as any).toDate()

      // Normalize dates to UTC midnight and convert to Firestore Timestamp
      const normalizedData = {
        ...tripData,
        startDate: Timestamp.fromDate(normalizeToUTCMidnight(startDate)),
        endDate: Timestamp.fromDate(normalizeToUTCMidnight(endDate)),
      }

      await tripRef.set({
        ...normalizedData,
        id: tripId,
        tasksCompleted: 0,
        totalTasks: 0,
        createdAt: FieldValue.serverTimestamp(),
        updatedAt: FieldValue.serverTimestamp(),
      })

      return tripId
    } catch (error) {
      console.error("Error creating trip (server):", error)
      throw error
    }
  }

  /**
   * Update a trip
   * @param tripId Trip ID
   * @param updateData Trip update data
   * @returns True if successful
   */
  static async updateTrip(tripId: string, updateData: TripUpdateData): Promise<boolean> {
    try {
      const adminDb = await getAdminDb()
      const tripRef = adminDb.collection(this.COLLECTION).doc(tripId)

      await tripRef.update({
        ...updateData,
        updatedAt: FieldValue.serverTimestamp(),
      })

      return true
    } catch (error) {
      console.error("Error updating trip (server):", error)
      throw error
    }
  }

  /**
   * Delete a trip
   * @param tripId Trip ID
   * @returns True if successful
   */
  static async deleteTrip(tripId: string): Promise<boolean> {
    try {
      const adminDb = await getAdminDb()
      await adminDb.collection(this.COLLECTION).doc(tripId).delete()
      return true
    } catch (error) {
      console.error("Error deleting trip (server):", error)
      throw error
    }
  }

  /**
   * Get trip attendees (users with "going" status)
   * @param tripId Trip ID
   * @returns Array of user IDs
   */
  static async getTripAttendees(tripId: string): Promise<string[]> {
    try {
      const adminDb = await getAdminDb()
      const querySnapshot = await adminDb
        .collection(this.USER_TRIPS_COLLECTION)
        .where("tripId", "==", tripId)
        .where("status", "==", "going")
        .get()

      return querySnapshot.docs.map((doc) => doc.data().userId)
    } catch (error) {
      console.error("Error getting trip attendees (server):", error)
      return []
    }
  }

  /**
   * Get trip attendees with user details
   * @param tripId Trip ID
   * @returns Array of user trips with user details
   */
  static async getTripAttendeesWithDetails(tripId: string): Promise<(UserTrip & { user: User })[]> {
    try {
      const adminDb = await getAdminDb()
      const querySnapshot = await adminDb
        .collection(this.USER_TRIPS_COLLECTION)
        .where("tripId", "==", tripId)
        .where("status", "==", "going")
        .get()

      if (querySnapshot.empty) {
        return []
      }

      // Get user IDs
      const userIds = querySnapshot.docs.map((doc) => doc.data().userId)

      // Get user details in parallel
      const users = await UserServerService.getUsers(userIds)

      // Combine user trip data with user details
      const attendeesWithDetails = querySnapshot.docs
        .map((doc) => {
          const userTripData = doc.data() as UserTrip
          const user = users[userTripData.userId]

          if (user) {
            return { ...userTripData, user }
          }
          return null
        })
        .filter(Boolean) as (UserTrip & { user: User })[]

      return attendeesWithDetails
    } catch (error) {
      console.error("Error getting trip attendees with details (server):", error)
      return []
    }
  }

  /**
   * Update trip status
   * @param tripId Trip ID
   * @param status New status
   * @returns True if successful
   */
  static async updateTripStatus(tripId: string, status: TripStatus): Promise<boolean> {
    try {
      return await this.updateTrip(tripId, { status })
    } catch (error) {
      console.error("Error updating trip status (server):", error)
      throw error
    }
  }

  /**
   * Get current user's upcoming trips
   * @param userId User ID
   * @returns Array of user's upcoming trips
   */
  static async getCurrentUserUpcomingTrips(userId: string): Promise<Trip[]> {
    try {
      return await this.getUserUpcomingTrips(userId)
    } catch (error) {
      console.error("Error getting current user upcoming trips (server):", error)
      throw error
    }
  }
}
